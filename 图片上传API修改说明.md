# 图片上传API统一修改说明

## 修改概述

已成功将三个页面的图片上传功能统一修改为使用新的 `/api/minio/upload` API，确保所有页面都使用相同的上传接口和参数规范。

## 修改的文件

### 1. pages/goldRecycle/index.js - 黄金回收页面
**修改内容：**
- ✅ 已经在使用 `/api/minio/upload` API
- ✅ 修改 bucketName 为 'mall'（原来已经是 'mall'）
- ✅ 更新API响应处理逻辑：从 `code === 200` 改为 `code === 0`
- ✅ 简化响应处理：只检查 `data.url` 字段，移除 `presignedUrl` 的检查

### 2. pages/verification/index.js - 身份认证页面
**修改内容：**
- ✅ 已经在使用 `/api/minio/upload` API
- ✅ 修改 bucketName 从 'verification-images' 改为 'mall'
- ✅ 更新API响应处理逻辑：从 `code === 200` 改为 `code === 0`
- ✅ 简化响应处理：只检查 `data.url` 字段，移除 `presignedUrl` 的检查

### 3. pages/wallet/recharge.js - 充值页面
**修改内容：**
- ✅ 完全替换上传逻辑，从旧的 walletApi.uploadFile 改为直接调用新API
- ✅ 添加新的 uploadImage 和 uploadSingleImage 方法
- ✅ 修改图片选择逻辑：选择后立即上传，而不是在提交时上传
- ✅ 简化提交逻辑：直接使用已上传的图片URL
- ✅ 使用 bucketName 为 'mall'
- ✅ 使用新的API响应格式：`code === 0` 和 `data.url`

### 4. utils/walletapi.js - API工具模块
**修改内容：**
- ✅ 更新 uploadFile 方法使用新的 `/api/minio/upload` API
- ✅ 添加 bucketName 参数设置为 'mall'
- ✅ 更新响应处理逻辑适配新的API格式
- ✅ 保持向后兼容的响应格式

## 新的API规范

### 接口信息
- **接口地址**: `/api/minio/upload`
- **请求方式**: `POST`
- **请求数据类型**: `multipart/form-data`

### 请求参数
- `file`: 要上传的文件（必需）
- `bucketName`: 固定设置为 `"mall"`（必需）

### 响应格式
```javascript
{
  "code": 0,           // 0表示成功
  "data": {
    "url": "上传后的文件访问URL"
  },
  "message": "上传成功"
}
```

## 功能特点

### 1. 统一的上传体验
- 所有页面都使用相同的上传API和参数
- 统一的错误处理和用户提示
- 一致的上传进度显示

### 2. 即时上传
- 用户选择图片后立即上传到服务器
- 避免在提交表单时再次上传，提升用户体验
- 减少表单提交时的等待时间

### 3. 错误处理
- 完善的网络错误处理
- 友好的用户提示信息
- 上传失败时的重试机制

### 4. 安全性
- 使用Bearer Token进行身份验证
- 统一的权限控制

## 测试建议

建议在以下场景进行测试：

1. **黄金回收页面**
   - 上传1-4张图片
   - 删除已上传的图片
   - 预览图片功能

2. **身份认证页面**
   - 上传身份证正面照片
   - 上传身份证反面照片
   - 重新上传替换图片

3. **充值页面**
   - 上传转账截图
   - 删除并重新上传图片
   - 预览上传的图片

4. **网络异常测试**
   - 网络断开时的上传行为
   - 上传大文件时的处理
   - 服务器错误时的用户提示

## 注意事项

1. 确保所有页面的token获取方式一致
2. 检查图片预览功能是否正常工作
3. 验证上传后的URL是否可以正常访问
4. 确认删除图片功能不会影响已上传的文件
