/* pages/wallet/withdraw.wxss */
.container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 提现金额部分 */
.withdraw-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.withdraw-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.withdraw-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.withdraw-balance {
  font-size: 28rpx;
  color: #666;
}

.amount-input-section {
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
}

.currency-symbol {
  font-size: 48rpx;
  color: #333;
  margin-right: 20rpx;
}

.amount-input {
  flex: 1;
  font-size: 48rpx;
  color: #333;
}

.quick-amounts {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.quick-amount {
  background: #f8f8f8;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 提现方式部分 */
.withdraw-methods {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.method-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f8f8;
  transition: all 0.3s;
}

.method-item.selected {
  background: #e6f7ff;
  border: 2rpx solid #1890ff;
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.method-icon image {
  width: 100%;
  height: 100%;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.method-account {
  font-size: 26rpx;
  color: #999;
}

.method-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 4rpx solid #999;
  border-right: 4rpx solid #999;
  transform: rotate(45deg);
}

/* 提现说明部分 */
.withdraw-notice {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.notice-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.notice-content {
  color: #666;
}

.notice-item {
  font-size: 26rpx;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

/* 提交按钮部分 */
.submit-section {
  padding: 40rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
}

.submit-btn.disabled {
  background: #ccc;
  color: #fff;
} 