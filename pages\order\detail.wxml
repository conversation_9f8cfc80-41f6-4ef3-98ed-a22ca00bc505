<!--pages/order/detail.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading">加载中...</view>
  </view>

  <!-- 订单详情 -->
  <block wx:if="{{!loading && order}}">
    <!-- 订单状态 -->
    <view class="status-card">
      <view class="status-icon status-{{order.status}}"></view>
      <view class="status-text">{{order.statusText}}</view>
      <view class="status-desc">
        <block wx:if="{{order.status === 1}}">
          我们已收到您的回收申请，正在安排专业人员处理
        </block>
        <block wx:elif="{{order.status === 2}}">
          专业人员正在鉴定您的黄金，请耐心等待
        </block>
        <block wx:elif="{{order.status === 3}}">
          回收已完成，感谢您的信任
        </block>
        <block wx:elif="{{order.status === 4}}">
          订单已取消：{{order.cancelReason || '用户取消'}}
        </block>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-card">
      <view class="card-title">收货信息</view>
      <view class="address-content">
        <view class="address-loading" wx:if="{{loadingAddress}}">
          <text class="loading-text">加载地址信息中...</text>
        </view>
        <block wx:elif="{{addressInfo}}">
          <view class="address-header">
            <view class="receiver-info">
              <text class="receiver-name">{{addressInfo.receiverName}}</text>
              <text class="receiver-phone">{{addressInfo.receiverPhone}}</text>
            </view>
            <view class="default-tag" wx:if="{{addressInfo.isDefault === 1}}">默认</view>
          </view>
          <view class="address-detail">{{addressInfo.fullAddress}}</view>
          <view class="address-extra" wx:if="{{addressInfo.postCode}}">
            <text class="postcode">邮编：{{addressInfo.postCode}}</text>
          </view>
        </block>
        <view class="address-fallback" wx:else>
          <view class="receiver-info">
            <text class="receiver-name">{{order.receiverName}}</text>
            <text class="receiver-phone">{{order.receiverPhone}}</text>
          </view>
          <view class="address-detail">{{order.receiverAddress || '地址信息获取中...'}}</view>
        </view>
      </view>
    </view>

    <!-- 物流信息 (仅在待取件状态显示) -->
    <view class="logistics-card" wx:if="{{order.status === 2}}">
      <view class="card-title">物流信息</view>

      <!-- 加载中 -->
      <view class="logistics-loading" wx:if="{{loadingExpress}}">
        <view class="loading-text">正在获取物流信息...</view>
      </view>

      <!-- 物流信息内容 -->
      <view class="logistics-content" wx:elif="{{expressInfo && !expressInfo.error}}">
        <view class="logistics-header">
          <view class="logistics-company">{{expressInfo.company}}</view>
          <view class="logistics-status">{{expressInfo.status}}</view>
        </view>

        <view class="logistics-number" wx:if="{{order.expressNumber}}">
          <text>运单号：{{order.expressNumber}}</text>
          <text class="copy-btn" bindtap="copyExpressNumber">复制</text>
        </view>

        <view class="logistics-time" wx:if="{{expressInfo.takeTime}}">
          <text>运输时长：{{expressInfo.takeTime}}</text>
        </view>

        <!-- 联系方式 -->
        <view class="logistics-contact" wx:if="{{expressInfo.courierPhone || expressInfo.comPhone}}">
          <view class="contact-item" wx:if="{{expressInfo.courierPhone}}" bindtap="callCourier">
            <text>快递员：{{expressInfo.courierPhone}}</text>
            <text class="call-btn">拨打</text>
          </view>
          <view class="contact-item" wx:if="{{expressInfo.comPhone}}" bindtap="callCompany">
            <text>客服：{{expressInfo.comPhone}}</text>
            <text class="call-btn">拨打</text>
          </view>
        </view>

        <!-- 物流轨迹 -->
        <view class="logistics-tracks" wx:if="{{expressInfo.hasTracking}}">
          <view class="tracks-title">物流轨迹</view>
          <view class="track-list">
            <view class="track-item" wx:for="{{expressInfo.tracks}}" wx:key="index">
              <view class="track-time">{{item.time}}</view>
              <view class="track-status">{{item.status}}</view>
              <view class="track-location" wx:if="{{item.location}}">{{item.location}}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 物流信息错误 -->
      <view class="logistics-error" wx:elif="{{expressInfo && expressInfo.error}}">
        <view class="error-text">{{expressInfo.message}}</view>
        <view class="retry-btn" bindtap="retryLoadExpress">重新获取</view>
      </view>

      <!-- 无物流信息 -->
      <view class="logistics-empty" wx:else>
        <view class="empty-text">暂无物流信息</view>
      </view>
    </view>

    <!-- 黄金信息 -->
    <view class="gold-card">
      <view class="card-title">黄金信息</view>
      <view class="gold-content">
        <view class="gold-image-section" wx:if="{{order.hasImage}}">
          <view class="image-gallery">
            <view
              class="image-item"
              wx:for="{{order.imageList}}"
              wx:key="*this"
              bindtap="previewImage"
              data-index="{{index}}"
            >
              <image
                class="gold-image"
                src="{{item}}"
                mode="aspectFill"
                lazy-load="{{true}}"
                binderror="onImageError"
                bindload="onImageLoad"
              ></image>
              <view class="image-index">{{index + 1}}/{{order.imageCount}}</view>
            </view>
          </view>
          <view class="image-tip">点击图片查看大图 (共{{order.imageCount}}张)</view>
        </view>
        <view class="no-image-section" wx:else>
          <view class="no-image-placeholder">
            <text class="no-image-text">暂无图片</text>
          </view>
        </view>
        <view class="gold-details">
          <view class="detail-item">
            <text class="detail-label">黄金类型:</text>
            <text class="detail-value">{{order.goldTypeText}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">纯度:</text>
            <text class="detail-value">{{order.purity}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">品相:</text>
            <text class="detail-value">{{order.goldCondition}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">预估重量:</text>
            <text class="detail-value">{{order.estimatedWeight}}g</text>
          </view>
          <view class="detail-item" wx:if="{{order.description}}">
            <text class="detail-label">描述:</text>
            <text class="detail-value">{{order.description}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="price-card" wx:if="{{order.goldType === 'jewelry'}}">
      <view class="card-title">价格信息</view>
      <view class="price-details">
        <view class="price-item">
          <text class="price-label">预估价格:</text>
          <text class="price-value">¥{{order.estimatedPrice}}</text>
        </view>
        <view class="price-item" wx:if="{{order.finalPrice && order.finalPrice !== '0.00'}}">
          <text class="price-label">最终价格:</text>
          <text class="price-value final-price">¥{{order.finalPrice}}</text>
        </view>
        <view class="price-item" wx:if="{{order.inspectionResult}}">
          <text class="price-label">鉴定结果:</text>
          <text class="price-value">{{order.inspectionResult}}</text>
        </view>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-card">
      <view class="card-title">订单信息</view>
      <view class="order-info-list">
        <view class="order-info-item">
          <text class="info-label">订单编号</text>
          <view class="info-value">
            <text>{{order.orderId}}</text>
            <text class="copy-btn" bindtap="copyOrderId">复制</text>
          </view>
        </view>
        <view class="order-info-item">
          <text class="info-label">用户账号</text>
          <text class="info-value">{{order.account}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">创建时间</text>
          <text class="info-value">{{order.createTimeFormatted}}</text>
        </view>
        <view class="order-info-item">
          <text class="info-label">更新时间</text>
          <text class="info-value">{{order.updateTimeFormatted}}</text>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <!-- 待处理状态 -->
      <block wx:if="{{order.status === 1}}">
        <view class="bottom-action cancel" bindtap="showCancel">取消订单</view>
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 处理中状态 -->
      <block wx:if="{{order.status === 2}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 已完成状态 -->
      <block wx:if="{{order.status === 3}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>

      <!-- 已取消状态 -->
      <block wx:if="{{order.status === 4}}">
        <view class="bottom-action" bindtap="contactService">联系客服</view>
      </block>
    </view>
  </block>

  <!-- 订单不存在 -->
  <view class="empty-container" wx:if="{{!loading && !order}}">
    <image class="empty-icon" src="/images/icons/empty-order.png"></image>
    <view class="empty-text">订单不存在或已被删除</view>
    <navigator url="/pages/order/list" class="go-back">返回订单列表</navigator>
  </view>

  <!-- 取消订单面板 -->
  <view class="modal {{showCancelModal ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">取消订单</view>
        <view class="modal-close" bindtap="hideCancel">×</view>
      </view>
      <view class="modal-body">
        <view class="cancel-title">请选择取消原因</view>
        <view class="cancel-reasons">
          <view
            class="cancel-reason-item {{selectedReason === item ? 'selected' : ''}}"
            wx:for="{{cancelReasons}}"
            wx:key="*this"
            bindtap="selectReason"
            data-reason="{{item}}"
          >
            {{item}}
          </view>
        </view>
        <view class="cancel-other" wx:if="{{selectedReason === '其他原因'}}">
          <input
            class="cancel-input"
            placeholder="请输入取消原因"
            value="{{cancelReason}}"
            bindinput="inputCancelReason"
          />
        </view>
        <view class="cancel-button" bindtap="confirmCancel">确认取消</view>
      </view>
    </view>
  </view>

</view>
