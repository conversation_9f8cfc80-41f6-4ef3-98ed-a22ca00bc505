// pages/goldRecycle/index.js
const app = getApp();

Page({
  data: {
    goldPrice: {
      buyPrice: 0,
      updateTime: ''
    },
    goldTypes: [
      { id: 'jewelry', name: '黄金', icon: '/images/icons/jewelry.jpg' },
      { id: 'bar', name: '铂金', icon: '/images/icons/gold-bar.jpg' },
      { id: 'broken', name: '钯金', icon: '/images/icons/broken-gold.jpg' },
      { id: 'other', name: '其他', icon: '/images/icons/other-gold.jpg' }
    ],
    selectedType: '',
    estimatedWeight: '',
    description: '',
    images: [],
    // 示例图片 - 使用已有的图标作为示例
    exampleImages: [
      '/images/icons/jewelry.jpg'
    ],
    address: null,
    contactName: '',
    contactPhone: '',
    showAddressModal: false,
    submitLoading: false,
    step: 1, // 1: 选择类型, 2: 填写信息, 3: 提交成功
    recycleId: '', // 回收订单ID
    estimatedAmount: 0, // 预估金额
    // 新增手续费相关字段
    feeRate: 0, // 手续费率（元/克）
    totalFee: 0, // 总手续费
    userLevel: '', // 用户等级
    userLevelName: '', // 用户等级名称
    currentStep: 1, // 当前步骤
    goldPrice: '--', // 今日金价
    userLevelName: '普通用户', // 用户等级名称
    feeRate: '0.00', // 服务费率
    userLevel: 0, // 用户等级
    estimatedAmount: '0.00', // 预估金额
    serviceFee: '0.00', // 总服务费
    finalAmount: '0.00', // 最终金额
    selectedGoldType: null, // 选中的金种
    weight: '', // 称重
    purity: '', // 纯度
    condition: '1', // 品相
    uploading: false, // 是否正在上传图片
    canSubmit: false, // 是否可以提交
    selectedAddress: null, // 新增：选中的收货地址

    // 成色选项数据
    purityOptions: {
      jewelry: [ // 黄金
        { value: 'Au9999', label: 'Au9999' },
        { value: 'Au999', label: 'Au999' },
        { value: '足金', label: '足金' },
        { value: 'Au916', label: 'Au916' },
        { value: 'Au750', label: 'Au750' },
        { value: '其他', label: '其他（本店只收Au750及以上成色）' }
      ],
      bar: [ // 铂金
        { value: 'Pt999', label: 'Pt999' },
        { value: 'Pt990', label: 'Pt990' },
        { value: 'Pt950', label: 'Pt950' },
        { value: '其他', label: '其他（本店只收Pt900及以上成色）' }
      ],
      broken: [ // 钯金
        { value: 'Pd999', label: 'Pd999' },
        { value: 'Pd990', label: 'Pd990' },
        { value: 'Pd950', label: 'Pd950' },
        { value: '其他', label: '其他（本店只收Pd900及以上成色）' }
      ],
      other: [ // 未知类型
        { value: '其他', label: '其他' }
      ]
    },
    currentPurityOptions: [], // 当前显示的成色选项
    showPurityPicker: false, // 是否显示成色选择器
    selectedPurityIndex: -1 // 选中的成色索引
  },

  onLoad: function (options) {
    // 首先检查用户登录状态和等级
    if (!this.checkUserVerification()) {
      return; // 如果用户未认证，直接返回，不继续加载页面内容
    }

    this.loadGoldPrice();
    console.log("开始加载用户等级信息")
    this.loadUserLevel(); // 加载用户等级信息
    console.log("加载用户等级信息结束")

    // 初始化数据
    this.setData({
      selectedAddress: null,
      canSubmit: false
    });

    // 如果用户已登录，获取默认地址
    if (app.globalData.openid) {
      this.loadUserAddress();
    }

    // 加载示例图片
    this.loadExampleImage();
  },

  // 检查用户认证状态
  checkUserVerification: function() {
    // 检查用户是否登录
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');

    if (!userInfo || !token) {
      // 用户未登录，跳转到登录页面
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行黄金回收',
        showCancel: false,
        confirmText: '去登录',
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return false;
    }

    // 检查用户等级，如果为0级则需要实名认证
    if (userInfo.userLevel === 0 || userInfo.userLevel === undefined || userInfo.userLevel === null) {
      wx.showModal({
        title: '需要实名认证',
        content: '您需要完成实名认证后才可以回收黄金，是否前往认证？',
        confirmText: '去认证',
        cancelText: '暂不认证',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确认，跳转到实名认证页面
            wx.navigateTo({
              url: '/pages/verification/index'
            });
          } else {
            // 用户点击取消，返回上一页或首页
            wx.navigateBack({
              delta: 1,
              fail: () => {
                // 如果没有上一页，跳转到首页
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }
            });
          }
        }
      });
      return false;
    }

    // 用户已登录且已认证，可以继续使用页面
    return true;
  },

  // 加载示例图片
  loadExampleImage: function() {
    const that = this;
    // 使用一个已知存在的图片作为示例
    wx.getImageInfo({
      src: '/images/icons/jewelry.jpg',
      success: function(res) {
        console.log('获取图片信息成功:', res.path);
        that.setData({
          exampleImages: [res.path]
        });
      },
      fail: function(err) {
        console.error('获取图片信息失败:', err);
        // 尝试使用另一个图片
        wx.getImageInfo({
          src: '/images/tabbar/home.png',
          success: function(res) {
            console.log('获取备用图片信息成功:', res.path);
            that.setData({
              exampleImages: [res.path]
            });
          },
          fail: function(err) {
            console.error('获取备用图片信息也失败:', err);
          }
        });
      }
    });
  },

  onShow: function () {
    // 每次页面显示时都检查用户认证状态
    if (!this.checkUserVerification()) {
      return; // 如果用户未认证，直接返回
    }

    // 页面显示时刷新金价
    this.loadGoldPrice();

    // 检查是否有从地址列表页面传递过来的 selectedAddress
    const selectedAddress = this.data.selectedAddress; // onShow时data已经更新

    console.log('onShow - selectedAddress:', selectedAddress);

    if (selectedAddress) {
      // 确保更新联系人信息
      this.setData({
        contactName: selectedAddress.receiverName || '',
        contactPhone: selectedAddress.receiverPhone || ''
      });

      // 手动调用 setAddress 函数处理地址数据
      this.setAddress(selectedAddress);
    } else {
      // 如果没有传递地址回来，可能是第一次进入或取消选择
      // 尝试加载默认地址
      this.loadUserAddress();
    }

    // 重新计算金额以更新按钮状态
    this.checkCanSubmit();
  },

  // 从API加载金价
  loadGoldPrice: function () {
    wx.showLoading({
      title: '加载中...',
    });

    wx.request({
      url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.code === 1) {
          let goldData = {};

          // 检查数据结构
          if (res.data.data && res.data.data.list) {
            goldData = res.data.data.list;
          } else if (res.data.data) {
            goldData = res.data.data;
          }

          // 提取黄金9999的数据作为主要回收金价
          let mainGold = {};

          if (goldData.Au9999) {
            mainGold = goldData.Au9999;
          } else if (Array.isArray(goldData) && goldData.length > 0) {
            // 如果是数组，查找黄金9999
            const au9999Item = goldData.find(item => item.type === 'Au9999' || item.typename === '黄金9999');
            if (au9999Item) {
              mainGold = au9999Item;
            } else if (goldData.length > 0) {
              // 如果没有找到黄金9999，使用第一个数据
              mainGold = goldData[0];
            }
          }

          // 回收价通常比卖出价低一些，这里使用买入价作为回收价
          let buyPrice = 55230; // 默认值

          if (mainGold.buyprice) {
            buyPrice = parseFloat(mainGold.buyprice) * 100;
          } else if (mainGold.price) {
            // 如果没有买入价，使用价格并稍微降低一点作为回收价
            buyPrice = parseFloat(mainGold.price) * 0.98 * 100;
          }

          this.setData({
            'goldPrice.buyPrice': buyPrice,
            'goldPrice.updateTime': mainGold.updatetime || this.formatTime(new Date())
          });

          // 如果已经输入了重量，重新计算预估金额
          if (this.data.estimatedWeight) {
            const amount = parseFloat(this.data.estimatedWeight) * buyPrice;
            this.setData({
              estimatedAmount: amount
            });
          }
        } else {
          console.error('获取金价数据失败', res);
          // 使用默认数据
          this.setDefaultGoldPrice();
        }

        wx.hideLoading();
      },
      fail: (err) => {
        console.error('请求金价API失败', err);
        // 使用默认数据
        this.setDefaultGoldPrice();
        wx.hideLoading();
      }
    });
  },

  // 设置默认金价数据（当API请求失败时使用）
  setDefaultGoldPrice: function() {
    const buyPrice = 55230; // 552.30 * 100
    this.setData({
      'goldPrice.buyPrice': buyPrice,
      'goldPrice.updateTime': this.formatTime(new Date())
    });

    // 如果已经输入了重量，重新计算预估金额
    if (this.data.estimatedWeight) {
      const amount = parseFloat(this.data.estimatedWeight) * buyPrice;
      this.setData({
        estimatedAmount: amount
      });
    }
  },

  // 加载用户地址
  loadUserAddress: function () {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      return;
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    const apiData = {
      account: userInfo.account
    };

    const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/user/address/list`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200 && res.data.data && res.data.data.length > 0) {
          // 查找默认地址
          let defaultAddress = res.data.data.find(item => item.isDefault === 1);

          // 如果没有默认地址，使用第一个地址
          if (!defaultAddress && res.data.data.length > 0) {
            defaultAddress = res.data.data[0];
          }

          if (defaultAddress) {
            this.setData({
              selectedAddress: defaultAddress,
              contactName: defaultAddress.receiverName,
              contactPhone: defaultAddress.receiverPhone
            });

            // 检查是否可以提交
            this.checkCanSubmit();
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取用户地址失败', err);
      }
    });
  },

  // 选择黄金类型
  selectGoldType: function (e) {
    const typeId = e.currentTarget.dataset.type;
    // 查找对应的类型对象
    const selectedType = this.data.goldTypes.find(item => item.id === typeId);

    // 更新成色选项
    const currentPurityOptions = this.data.purityOptions[typeId] || [];

    this.setData({
      selectedType: typeId,
      selectedGoldType: selectedType,
      currentPurityOptions: currentPurityOptions,
      purity: '', // 重置成色选择
      selectedPurityIndex: -1 // 重置选中索引
    });

    // 如果已经输入了重量，重新计算金额
    if (this.data.estimatedWeight || this.data.weight) {
      const weight = this.data.estimatedWeight || this.data.weight;
      this.calculateAmount(weight);
    }

    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 输入重量
  inputWeight: function (e) {
    const weight = e.detail.value;
    this.setData({
      estimatedWeight: weight,
      weight: weight // 同时设置 weight 字段
    });

    this.calculateAmount(weight);
    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 显示成色选择器
  showPuritySelector: function () {
    if (this.data.currentPurityOptions.length === 0) {
      wx.showToast({
        title: '请先选择金属类型',
        icon: 'none'
      });
      return;
    }
    this.setData({
      showPurityPicker: true
    });
  },

  // 隐藏成色选择器
  hidePuritySelector: function () {
    this.setData({
      showPurityPicker: false
    });
  },

  // 选择成色
  selectPurity: function (e) {
    const index = e.currentTarget.dataset.index;
    const selectedOption = this.data.currentPurityOptions[index];

    this.setData({
      purity: selectedOption.value,
      selectedPurityIndex: index,
      showPurityPicker: false
    });

    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 输入纯度（保留原函数以防其他地方调用）
  inputPurity: function (e) {
    this.setData({
      purity: e.detail.value
    });
    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 输入品相
  inputCondition: function (e) {
    this.setData({
      condition: e.detail.value
    });
    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 输入描述
  inputDescription: function (e) {
    this.setData({
      description: e.detail.value
    });
  },

  // 选择并上传图片
  chooseImage: function () {
    const that = this;

    // 检查当前图片数量
    if (this.data.images.length >= 4) {
      wx.showToast({
        title: '最多只能上传4张图片',
        icon: 'none'
      });
      return;
    }

    const remainingCount = 4 - this.data.images.length;

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        // 选择图片成功后，立即上传到服务器
        that.uploadImages(res.tempFilePaths);
      },
      fail: function (err) {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 批量上传图片
  uploadImages: function (tempFilePaths) {
    if (!tempFilePaths || tempFilePaths.length === 0) {
      return;
    }

    // 设置上传状态
    this.setData({
      uploading: true
    });

    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    // 记录上传成功的图片URL
    const uploadedUrls = [];
    let uploadedCount = 0;
    const totalCount = tempFilePaths.length;

    // 逐个上传图片
    tempFilePaths.forEach((filePath, index) => {
      this.uploadSingleImage(filePath)
        .then((imageUrl) => {
          uploadedUrls[index] = imageUrl;
          uploadedCount++;

          // 更新上传进度
          wx.showLoading({
            title: `上传中 ${uploadedCount}/${totalCount}`,
            mask: true
          });

          // 检查是否全部上传完成
          if (uploadedCount === totalCount) {
            this.handleUploadComplete(uploadedUrls);
          }
        })
        .catch((error) => {
          console.error(`图片 ${index + 1} 上传失败:`, error);
          uploadedCount++;

          // 即使有图片上传失败，也要检查是否全部处理完成
          if (uploadedCount === totalCount) {
            this.handleUploadComplete(uploadedUrls);
          }
        });
    });
  },

  // 上传单个图片
  uploadSingleImage: function (filePath) {
    return new Promise((resolve, reject) => {
      // 检查用户登录状态
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      wx.uploadFile({
        url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
        filePath: filePath,
        name: 'file', // 服务器接收文件的字段名
        header: {
          'Authorization': `Bearer ${token}`
        },
        formData: {
          'bucketName': 'mall' // 可以根据需要设置bucket名称
        },
        success: (res) => {
          console.log('图片上传API响应:', res);

          try {
            // 解析响应数据
            const responseData = JSON.parse(res.data);
            console.log('解析后的响应数据:', responseData);

            // 修正判断条件：API返回的是 code: 200 表示成功
            if (responseData.code === 200 && responseData.data && responseData.data.presignedUrl) {
              // 上传成功，获取图片URL
              const imageUrl = responseData.data.presignedUrl;
              console.log('图片上传成功，URL:', imageUrl);
              resolve(imageUrl);
            } else if (responseData.code === 200 && responseData.data && responseData.data.url) {
              // 如果没有presignedUrl，尝试使用url字段
              const imageUrl = responseData.data.url;
              console.log('图片上传成功，使用URL字段:', imageUrl);
              resolve(imageUrl);
            } else {
              console.error('图片上传失败，API返回:', responseData);
              reject(new Error(responseData.message || '上传失败'));
            }
          } catch (parseError) {
            console.error('解析上传响应失败:', parseError, '原始响应:', res.data);
            reject(new Error('解析响应失败'));
          }
        },
        fail: (error) => {
          console.error('图片上传请求失败:', error);
          reject(error);
        }
      });
    });
  },

  // 处理上传完成
  handleUploadComplete: function (uploadedUrls) {
    wx.hideLoading();

    // 过滤掉上传失败的图片（undefined或null）
    const validUrls = uploadedUrls.filter(url => url && url.trim() !== '');

    if (validUrls.length > 0) {
      // 将成功上传的图片URL添加到images数组
      this.setData({
        images: this.data.images.concat(validUrls),
        uploading: false
      });

      wx.showToast({
        title: `成功上传${validUrls.length}张图片`,
        icon: 'success'
      });

      // 检查是否可以提交
      this.checkCanSubmit();
    } else {
      // 所有图片都上传失败
      this.setData({
        uploading: false
      });

      wx.showToast({
        title: '图片上传失败，请重试',
        icon: 'none'
      });
    }

    // 如果有部分图片上传失败
    const failedCount = uploadedUrls.length - validUrls.length;
    if (failedCount > 0 && validUrls.length > 0) {
      setTimeout(() => {
        wx.showToast({
          title: `${failedCount}张图片上传失败`,
          icon: 'none'
        });
      }, 2000);
    }
  },

  // 预览图片
  previewImage: function (e) {
    const current = e.currentTarget.dataset.src;
    wx.previewImage({
      current: current,
      urls: this.data.images
    });
  },

  // 预览示例图片
  previewExampleImage: function (e) {
    const current = e.currentTarget.dataset.src;
    console.log('预览示例图片:', current);

    // 使用固定的图片路径
    const imageUrl = '/images/gold-example.png';

    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl],
      success: function() {
        console.log('预览图片成功');
      },
      fail: function(err) {
        console.error('预览图片失败:', err);

        // 如果预览失败，尝试使用wx.showToast显示错误信息
        wx.showToast({
          title: '图片预览失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });

        // 尝试使用另一种方式显示图片
        wx.navigateTo({
          url: '/pages/imagePreview/index?src=' + encodeURIComponent(imageUrl)
        });
      }
    });
  },

  // 删除图片
  deleteImage: function (e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.images]; // 创建副本避免直接修改原数组

    // 获取要删除的图片URL（用于日志记录）
    const deletedImageUrl = images[index];
    console.log('删除图片:', deletedImageUrl);

    // 从数组中移除图片
    images.splice(index, 1);

    this.setData({
      images: images
    });

    wx.showToast({
      title: '图片已删除',
      icon: 'success',
      duration: 1000
    });

    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 显示地址选择
  showAddressSelect: function () {
    this.setData({
      showAddressModal: true
    });
  },

  // 隐藏地址选择
  hideAddressSelect: function () {
    this.setData({
      showAddressModal: false
    });
  },

  // 选择地址 - 跳转到地址管理页面
  selectAddress: function () {
    wx.navigateTo({
      url: '/pages/address/index?from=goldRecycle'
    });
  },

  // 接收从地址管理页面选择的地址
  setAddress: function (address) {
    console.log('setAddress 被调用，地址数据:', address);

    if (address) {
      // 确保地址对象包含必要的字段
      if (!address.receiverName || !address.receiverPhone) {
        console.warn('地址数据缺少必要字段:', address);
      }

      this.setData({
        selectedAddress: address,
        contactName: address.receiverName || '',
        contactPhone: address.receiverPhone || ''
      });

      console.log('地址已设置:', this.data.selectedAddress);

      // 检查是否可以提交
      this.checkCanSubmit();
    } else {
      console.warn('setAddress 被调用，但地址数据为空');
    }
  },

  // 输入联系人
  inputContactName: function (e) {
    this.setData({
      contactName: e.detail.value
    });
    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 输入联系电话
  inputContactPhone: function (e) {
    this.setData({
      contactPhone: e.detail.value
    });
    // 检查是否可以提交
    this.checkCanSubmit();
  },

  // 下一步
  nextStep: function () {
    if (!this.data.selectedType) {
      wx.showToast({
        title: '请选择黄金类型',
        icon: 'none'
      });
      return;
    }

    this.setData({
      step: 2
    });
  },

  // 上一步
  prevStep: function () {
    this.setData({
      step: 1
    });
  },

  // 账户扣费API调用
  deductAccountFee: function(accountId, amount) {
    return true
    // return new Promise((resolve, reject) => {
    //   wx.request({
    //     url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${accountId}/fee?amount=${amount}`,
    //     method: 'POST',
    //     header: {
    //       'Authorization': `Bearer ${wx.getStorageSync('token')}`,
    //       'Content-Type': 'application/x-www-form-urlencoded'
    //     },
    //     success: (res) => {
    //       console.log('账户扣费API响应:', res);
    //       if (res.data.code === 200) {
    //         if (res.data.data === true) {
    //           // 扣费成功
    //           console.log('账户扣费成功');
    //           resolve(res.data);
    //         } else {
    //           // 扣费失败
    //           console.error('账户扣费失败，data为:', res.data.data);
    //           reject(new Error('账户余额不足或扣费失败'));
    //         }
    //       } else {
    //         console.error('账户扣费API返回错误:', res.data);
    //         reject(new Error(res.data.message || '扣费失败'));
    //       }
    //     },
    //     fail: (error) => {
    //       console.error('账户扣费请求失败:', error);
    //       reject(new Error('网络错误，请稍后重试'));
    //     }
    //   });
    // });
  },

  // 提交回收申请
  submitRecycle: function () {
    // 再次检查是否可以提交
    if (!this.checkCanSubmit()) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      });
      return;
    }

    // 设置提交中状态
    this.setData({
      submitLoading: true
    });

    // 直接提交订单，不再扣除手续费
    wx.showLoading({
      title: '提交订单中...',
      mask: true
    });

    this.submitOrder();
  },

  // 提交订单（从原来的submitRecycle函数中分离出来）
  submitOrder: function() {
    // 准备API请求数据
    const apiData = {
      account: app.globalData.userInfo.account,
      description: this.data.description, // 描述信息
      estimatedWeight: parseFloat(this.data.weight), // 预估重量
      goldCondition: this.data.condition, // 黄金状态
      goldType: this.data.selectedType, // 黄金类型
      images: this.data.images.length > 0 ? this.data.images : [], // 图片数组
      purity: this.data.purity, // 纯度
      addressId: this.data.selectedAddress.addressId , // 收件地址
      receiverName: this.data.contactName, // 收件人姓名
      receiverPhone: this.data.contactPhone, // 收件人电话
      estimatedPrice: this.data.estimatedAmount/100
    };

    console.log('提交订单数据:', apiData);

    // 发送真实API请求
    wx.request({
      url: app.globalData.apiConfig.baseUrl + '/api/gold-recycle/orders',
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': wx.getStorageSync('token') ? 'Bearer ' + wx.getStorageSync('token') : ''
      },
      data: apiData,
      success: (res) => {
        wx.hideLoading();
        console.log('订单提交API响应:', res);

        if (res.statusCode === 200 && res.data) {
          // API请求成功
          let orderId;

          // 根据API响应格式获取订单ID
          if (res.data.code === 200) {
            // 如果返回的是字符串，直接使用
            if (typeof res.data.data === 'string') {
              orderId = res.data.data;
            }
            // 如果返回的是对象，尝试获取其中的ID字段
            else if (typeof res.data.data === 'object' && res.data.data !== null) {
              orderId = res.data.data.id || res.data.data.orderId || res.data.data;
            }
            // 如果没有返回数据，生成一个临时ID
            else {
              orderId = 'R' + new Date().getTime();
            }

            console.log('订单提交成功，订单ID:', orderId);

            // 更新状态为提交成功
            this.setData({
              submitLoading: false,
              step: 3, // 切换到成功页面
              recycleId: orderId
            });

            // 显示成功提示
            wx.showToast({
              title: '订单提交成功',
              icon: 'success',
              duration: 2000
            });
          } else {
            // API返回错误码，需要回滚手续费
            console.error('订单提交失败，需要回滚手续费:', res.data);
            this.handleOrderSubmitFailure(res.data.message || '订单提交失败');
          }
        } else {
          // API请求失败，需要回滚手续费
          console.error('订单提交请求失败:', res);
          this.handleOrderSubmitFailure('订单提交失败，请稍后重试');
        }
      },
      fail: (err) => {
        console.error('订单提交网络请求失败:', err);
        wx.hideLoading();
        this.handleOrderSubmitFailure('网络错误，请检查网络连接');
      }
    });
  },

  // 处理订单提交失败（需要考虑回滚手续费）
  handleOrderSubmitFailure: function(errorMessage) {
    this.setData({
      submitLoading: false
    });

    // 显示错误信息
    wx.showModal({
      title: '订单提交失败',
      content: `${errorMessage}\n\n手续费已扣除，如需退还请联系客服。`,
      showCancel: true,
      cancelText: '确定',
      confirmText: '联系客服',
      success: (res) => {
        if (res.confirm) {
          // 用户选择联系客服
          wx.makePhoneCall({
            phoneNumber: '************', // 替换为实际客服电话
            fail: () => {
              wx.showToast({
                title: '请拨打客服电话：************',
                icon: 'none',
                duration: 3000
              });
            }
          });
        }
      }
    });
  },

  // 查看回收订单
  viewRecycleOrder: function () {
    // 检查订单ID是否存在
    if (!this.data.recycleId) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/order/detail?id=' + this.data.recycleId
    });
  },

  // 返回首页
  goToHome: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 加载用户等级信息
  loadUserLevel: function() {
    console.log(app.globalData.userInfo.userLevel)
    const userLevel = app.globalData.userInfo.userLevel

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/platform-fees/${userLevel}`,
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          this.setData({
            userLevel: res.data.userLevel,
            userLevelName: res.data.levelName,
            feeRate: res.data.feeRate
          });
          // 如果已有重量，重新计算金额
          if (this.data.estimatedWeight) {
            this.calculateAmount(this.data.estimatedWeight);
          }
        }
      },
      fail: (err) => {
        console.error('获取用户等级失败', err);
      }
    });
  },

  // 计算金额（包含手续费）
  calculateAmount: function(weight) {
    if (!weight || !this.data.goldPrice.buyPrice || !this.data.feeRate) return;

    const weightValue = parseFloat(weight);
    const feeRateValue = parseFloat(this.data.feeRate);

    // 计算手续费（所有类型都需要）
    const serviceFee = (weightValue * feeRateValue).toFixed(2);

    if (this.data.selectedType === 'jewelry') {
      // 黄金类型：计算预估金额和最终金额
      const baseAmount = weightValue * this.data.goldPrice.buyPrice/100;
      const finalAmount = baseAmount - parseFloat(serviceFee);

      this.setData({
        estimatedAmount: baseAmount.toFixed(2),
        serviceFee: serviceFee,
        totalFee: serviceFee, // 保持兼容性
        finalAmount: finalAmount.toFixed(2)
      });
    } else {
      // 非黄金类型（铂金、钯金、其他）：只计算手续费
      this.setData({
        estimatedAmount: '0.00', // 不显示预估金额
        serviceFee: serviceFee,
        totalFee: serviceFee, // 保持兼容性
        finalAmount: '0.00' // 不显示最终金额
      });
    }

    console.log('计算结果:', {
      selectedType: this.data.selectedType,
      weight: weightValue,
      feeRate: feeRateValue,
      serviceFee: serviceFee,
      estimatedAmount: this.data.estimatedAmount,
      finalAmount: this.data.finalAmount
    });
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const {
      selectedType,
      estimatedWeight,
      weight,
      purity,
      condition,
      images,
      selectedAddress,
      contactName,
      contactPhone
    } = this.data;

    // 检查所有必填字段
    const canSubmit =
      selectedType &&
      (parseFloat(estimatedWeight) > 0 || parseFloat(weight) > 0) &&
      purity && purity.trim() !== '' &&
      condition && condition.trim() !== '' &&
      images && images.length > 0 &&
      selectedAddress &&
      contactName && contactName.trim() !== '' &&
      contactPhone && contactPhone.trim() !== '';

    console.log('检查提交条件:', {
      selectedType,
      weight: parseFloat(weight) > 0,
      purity: purity && purity.trim() !== '',
      condition: condition && condition.trim() !== '',
      images: images && images.length > 0,
      selectedAddress: !!selectedAddress,
      selectedAddressDetail: selectedAddress,
      contactName: contactName && contactName.trim() !== '',
      contactPhone: contactPhone && contactPhone.trim() !== '',
      canSubmit
    });

    // 更新 canSubmit 状态
    this.setData({
      canSubmit: canSubmit
    });

    return canSubmit;
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面，传递特定的订单号
    wx.navigateTo({
      url: '/pages/customerService/index?orderId=回收前咨询',
      fail: (error) => {
        console.error('跳转客服页面失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})
