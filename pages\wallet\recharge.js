// pages/wallet/recharge.js
const app = getApp();
const walletApi = require('../../utils/walletapi');

Page({
  data: {
    amount: '', // 充值金额
    phone: '', // 手机号码
    imageUrl: '', // 转账截图
    remark: '', // 备注信息
    submitting: false, // 是否正在提交
    submitDisabled: true // 是否禁用提交按钮
  },

  onLoad: function (options) {
    // 如果用户已登录，自动填充手机号
    if (app.globalData.userInfo && app.globalData.userInfo.phone) {
      this.setData({
        phone: app.globalData.userInfo.phone
      });
    }

    // 检查表单是否可提交
    this.checkFormValid();
  },

  // 输入充值金额
  inputAmount: function(e) {
    this.setData({
      amount: e.detail.value
    });
    this.checkFormValid();
  },

  // 输入手机号码
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
    this.checkFormValid();
  },

  // 输入备注信息
  inputRemark: function(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        // 立即上传图片
        this.uploadImage(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 上传图片
  uploadImage: function(filePath) {
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });

    this.uploadSingleImage(filePath)
      .then((imageUrl) => {
        wx.hideLoading();

        this.setData({
          imageUrl: imageUrl
        });

        this.checkFormValid();

        wx.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      })
      .catch((error) => {
        wx.hideLoading();
        console.error('图片上传失败:', error);

        wx.showToast({
          title: '图片上传失败，请重试',
          icon: 'none'
        });
      });
  },

  // 上传单个图片到minio
  uploadSingleImage: function (filePath) {
    return new Promise((resolve, reject) => {
      // 检查用户登录状态
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('用户未登录'));
        return;
      }

      wx.uploadFile({
        url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
        filePath: filePath,
        name: 'file', // 服务器接收文件的字段名
        header: {
          'Authorization': `Bearer ${token}`
        },
        formData: {
          'bucketName': 'mall' // 统一使用mall bucket
        },
        success: (res) => {
          console.log('图片上传API响应:', res);

          try {
            // 解析响应数据
            const responseData = JSON.parse(res.data);
            console.log('解析后的响应数据:', responseData);

            // 判断API响应：code为0表示成功
            if (responseData.code === 0 && responseData.data && responseData.data.url) {
              // 上传成功，获取图片URL
              const imageUrl = responseData.data.url;
              console.log('图片上传成功，URL:', imageUrl);
              resolve(imageUrl);
            } else {
              console.error('图片上传失败，API返回:', responseData);
              reject(new Error(responseData.message || '上传失败'));
            }
          } catch (parseError) {
            console.error('解析响应数据失败:', parseError);
            reject(new Error('解析响应失败'));
          }
        },
        fail: (uploadError) => {
          console.error('图片上传请求失败:', uploadError);
          reject(new Error('网络错误，请重试'));
        }
      });
    });
  },

  // 预览图片
  previewImage: function() {
    wx.previewImage({
      urls: [this.data.imageUrl],
      current: this.data.imageUrl
    });
  },

  // 删除图片
  deleteImage: function() {
    this.setData({
      imageUrl: ''
    });
    this.checkFormValid();
  },

  // 检查表单是否有效
  checkFormValid: function() {
    const { amount, phone, imageUrl } = this.data;
    const isValid = amount && phone && imageUrl && this.isValidPhone(phone);

    this.setData({
      submitDisabled: !isValid
    });
  },

  // 验证手机号
  isValidPhone: function(phone) {
    return /^1[3-9]\d{9}$/.test(phone);
  },

  // 提交充值申请
  submitRecharge: function() {
    if (this.data.submitDisabled || this.data.submitting) {
      return;
    }

    // 表单验证
    if (!this.data.amount) {
      wx.showToast({
        title: '请输入充值金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.phone) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    if (!this.isValidPhone(this.data.phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!this.data.imageUrl) {
      wx.showToast({
        title: '请上传转账截图',
        icon: 'none'
      });
      return;
    }

    // 设置提交中状态
    this.setData({
      submitting: true
    });

    // 直接提交充值申请，因为图片已经上传完成
    this.submitRechargeRequest(this.data.imageUrl);
  },

  // 提交充值请求
  submitRechargeRequest: function(imageUrl) {
    const token = app.globalData.userInfo ? app.globalData.userInfo.token : '';
    const rechargeData = {
      amount: this.data.amount,
      phone: this.data.phone,
      imageUrl: imageUrl,
      remark: this.data.remark
    };

    // 使用API模块提交充值申请
    walletApi.submitRecharge(rechargeData, token)
      .then(res => {
        this.setData({
          submitting: false
        });

        if (res.code === 200) {
          // 充值申请提交成功
          wx.showToast({
            title: '充值申请已提交',
            icon: 'success'
          });

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.msg || '充值申请提交失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('充值申请提交失败', err);

        this.setData({
          submitting: false
        });

        wx.showToast({
          title: err.msg || '网络错误，请重试',
          icon: 'none'
        });
      });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  }
})
