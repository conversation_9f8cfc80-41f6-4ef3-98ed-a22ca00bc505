const app = getApp();

Page({
  data: {
    accountInfo: {
      balance: '0.00'
    },
    amount: '',
    quickAmounts: [100, 500, 1000, 2000],
    selectedMethod: '',
    alipayAccount: '',
    wechatAccount: '',
    bankAccount: '',
    canSubmit: false
  },

  onLoad() {
    this.getAccountInfo();
    this.getWithdrawAccounts();
  },

  // 获取账户信息
  getAccountInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) return;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${userInfo.account}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            accountInfo: {
              balance: res.data.data.balance.toFixed(2)
            }
          });
        }
      }
    });
  },

  // 获取提现账户信息
  getWithdrawAccounts() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) return;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/withdraw-accounts`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 200) {
          const accounts = res.data.data;
          this.setData({
            alipayAccount: accounts.alipay,
            wechatAccount: accounts.wechat,
            bankAccount: accounts.bank
          });
        }
      }
    });
  },

  // 输入金额
  onAmountInput(e) {
    const amount = e.detail.value;
    this.setData({
      amount,
      canSubmit: this.checkCanSubmit(amount)
    });
  },

  // 选择快捷金额
  selectQuickAmount(e) {
    const amount = e.currentTarget.dataset.amount;
    this.setData({
      amount: amount.toString(),
      canSubmit: this.checkCanSubmit(amount)
    });
  },

  // 选择提现方式
  selectMethod(e) {
    const method = e.currentTarget.dataset.method;
    const account = this.data[`${method}Account`];
    
    if (!account) {
      wx.showModal({
        title: '提示',
        content: '请先绑定提现账户',
        confirmText: '去绑定',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/wallet/bind-account?type=${method}`
            });
          }
        }
      });
      return;
    }

    this.setData({
      selectedMethod: method,
      canSubmit: this.checkCanSubmit(this.data.amount)
    });
  },

  // 检查是否可以提交
  checkCanSubmit(amount) {
    const numAmount = parseFloat(amount);
    return numAmount >= 100 && 
           numAmount <= parseFloat(this.data.accountInfo.balance) && 
           this.data.selectedMethod !== '';
  },

  // 提交提现申请
  submitWithdraw() {
    if (!this.data.canSubmit) return;

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) return;

    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/withdraw`,
      method: 'POST',
      data: {
        accountId: userInfo.account,
        amount: parseFloat(this.data.amount),
        type: this.data.selectedMethod.toUpperCase()
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200) {
          wx.showModal({
            title: '提交成功',
            content: '提现申请已提交，请等待处理',
            showCancel: false,
            success: () => {
              wx.navigateBack();
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '提交失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  }
}) 