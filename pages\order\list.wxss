/* pages/order/list.wxss */
.container {
  padding-bottom: 30rpx;
}

/* 标签栏 */
.tab-bar {
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
  margin-left: 20rpx;
}

.tab-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: var(--primary-color);
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-card {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
  font-weight: bold;
}

.order-status.pending {
  color: #FF9800;
}

.order-status.processing {
  color: #2196F3;
}

.order-status.completed {
  color: #4CAF50;
}

.order-status.cancelled {
  color: #9E9E9E;
}

/* 订单内容 */
.order-content {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
}

.order-info {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: center;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.info-value.price {
  color: #FF6B00;
  font-weight: bold;
}

.info-value.final-price {
  color: #FF4D4F;
  font-weight: bold;
}

/* 订单图片 */
.order-image {
  margin-left: 20rpx;
  flex-shrink: 0;
}

.gold-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1rpx solid #f0f0f0;
}

.order-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}



.order-actions {
  display: flex;
  justify-content: flex-end;
}

.order-action {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  border: 1rpx solid #ddd;
  color: #666;
}

.order-action.primary {
  background-color: var(--primary-color);
  color: #fff;
  border: 1rpx solid var(--primary-color);
}

.order-action.cancel {
  border: 1rpx solid #FF5252;
  color: #FF5252;
}

.order-time {
  padding: 10rpx 30rpx 20rpx;
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 加载状态 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.empty {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.go-shopping {
  padding: 16rpx 40rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
}
