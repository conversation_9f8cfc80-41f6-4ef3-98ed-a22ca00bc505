// 测试文件上传功能
const app = getApp();

/**
 * 测试新的文件上传API
 * @param {string} filePath - 文件路径
 * @returns {Promise} Promise对象
 */
function testUploadAPI(filePath) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    if (!token) {
      reject(new Error('用户未登录'));
      return;
    }

    wx.uploadFile({
      url: `${app.globalData.apiConfig.baseUrl}/api/minio/upload`,
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': `Bearer ${token}`
      },
      formData: {
        'bucketName': 'mall'
      },
      success: (res) => {
        console.log('上传API响应:', res);
        
        try {
          const responseData = JSON.parse(res.data);
          console.log('解析后的响应数据:', responseData);
          
          // 判断API响应：code为0表示成功
          if (responseData.code === 0 && responseData.data && responseData.data.url) {
            const imageUrl = responseData.data.url;
            console.log('图片上传成功，URL:', imageUrl);
            resolve(imageUrl);
          } else {
            console.error('图片上传失败，API返回:', responseData);
            reject(new Error(responseData.message || '上传失败'));
          }
        } catch (parseError) {
          console.error('解析响应数据失败:', parseError);
          reject(new Error('解析响应失败'));
        }
      },
      fail: (uploadError) => {
        console.error('图片上传请求失败:', uploadError);
        reject(new Error('网络错误，请重试'));
      }
    });
  });
}

module.exports = {
  testUploadAPI
};
