// pages/index/index.js
const app = getApp();

Page({
  data: {
    // 平台统计数据
    platformStats: {
      totalGold: '0', // 平台累计收金(千克)
    },
    // 城市统计数据
    cityStats: {
      yesterdaySell: '27.79', // 昨日卖出克数
      todaySell: '5.44', // 今日卖出克数
      records: [] // 轮播数据，将在loadCityStats中填充
    },
    // 金价数据
    goldPrice: {
      buyPrice: 0,
      sellPrice: 0,
      updateTime: '',
      internationalPrice: '',
      shanghaiPrice: '',
      changePercent: '+0.09%',
      isUp: true
    },
    // 市场行情数据（只显示黄金9999和铂金9995）
    marketData: [],
    news: []
  },

  onLoad: function (options) {
    // 检查是否有邀请参数
    if (options.inviter) {
      app.globalData.inviter = options.inviter;
    }

    // 加载所有数据
    this.loadPlatformStats();
    this.loadCityStats();

    // 加载金价数据
    this.loadGoldPriceFromAPI();
  },

  onShow: function () {

    // 刷新金价数据
    this.loadGoldPriceFromAPI();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    Promise.all([
      this.loadPlatformStats(),
      this.loadCityStats(),
      this.loadGoldPriceFromAPI()
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onShareAppMessage: function () {
    // 分享小程序
    return {
      title: '黄金回收销售，当前金价：' + this.data.goldPrice.sellPrice + '元/克',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 跳转到商品详情
  goToProductDetail: function (e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/detail?id=' + productId
    });
  },

  // 跳转到回收页面
  goToRecycle: function () {
    wx.navigateTo({
      url: '/pages/goldRecycle/index'
    });
  },

  // 跳转到商城页面
  goToShop: function () {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 跳转到分类商品列表
  goToCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    wx.switchTab({
      url: '/pages/shop/shop?category=' + category
    });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 加载平台统计数据
  loadPlatformStats: function() {
    return new Promise((resolve) => {
      // 这里应该从服务器获取平台统计数据
      // 模拟API调用
      setTimeout(() => {
        this.setData({
          platformStats: {
            totalGold: '38,149.33'
          }
        });
        resolve();
      }, 100);
    });
  },

  // 加载城市统计数据
  loadCityStats: function() {
    return new Promise((resolve) => {
      // 这里应该从服务器获取城市统计数据
      // 模拟API调用
      setTimeout(() => {
        // 模拟轮播数据
        const records = [
          { city: '重庆市', time: '昨日*黄', amount: '27.79' },
          { city: '送宁到*', time: '', amount: '5.44' },
          { city: '北京市', time: '今日*黄', amount: '18.65' },
          { city: '上海市', time: '昨日*黄', amount: '32.41' },
          { city: '广州市', time: '今日*黄', amount: '15.92' },
          { city: '深圳市', time: '昨日*黄', amount: '21.37' },
          { city: '成都市', time: '今日*黄', amount: '12.83' },
          { city: '杭州市', time: '昨日*黄', amount: '19.56' }
        ];

        this.setData({
          cityStats: {
            yesterdaySell: '27.79',
            todaySell: '5.44',
            records: records
          }
        });
        resolve();
      }, 100);
    });
  },

  // 从API加载金价数据
  loadGoldPriceFromAPI: function() {
    return new Promise((resolve) => {
      wx.showLoading({
        title: '加载中...',
      });

      wx.request({
        url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.code === 1) {
            const goldData = res.data.data.list;

            // 提取黄金9999的数据作为主要金价
            const mainGold = goldData.Au9999 || {};

            // 更新金价数据
            this.setData({
              goldPrice: {
                buyPrice: mainGold.buyprice || '0',
                sellPrice: mainGold.sellprice || '0',
                updateTime: mainGold.updatetime || '',
                internationalPrice: '',
                shanghaiPrice: mainGold.price || '0',
                changePercent: mainGold.changepercent || '+0.00%',
                isUp: (mainGold.changequantity && parseFloat(mainGold.changequantity) >= 0) ? true : false
              }
            });

            // 处理市场行情数据（只显示黄金9999和铂金9995）
            const marketData = [];

            // 提取黄金9999数据
            if (goldData.Au9999) {
              const au9999 = goldData.Au9999;
              marketData.push({
                id: 'Au9999',
                name: '黄金9999',
                buyPrice: au9999.buyprice || '-',
                sellPrice: au9999.sellprice || '-',
                maxPrice: au9999.maxprice || '-',
                minPrice: au9999.minprice || '-'
              });
            }

            // 提取铂金9995数据（注意API返回的是PT9995全大写）
            if (goldData.PT9995) {
              const pt9995 = goldData.PT9995;
              marketData.push({
                id: 'Pt9995',
                name: '铂金9995',
                buyPrice: pt9995.buyprice || '-',
                sellPrice: pt9995.sellprice || '-',
                maxPrice: pt9995.maxprice || '-',
                minPrice: pt9995.minprice || '-'
              });
            }

            this.setData({
              marketData: marketData
            });
          } else {
            console.error('获取金价数据失败', res);
            // 使用默认数据
            this.setDefaultGoldData();
          }

          wx.hideLoading();
          resolve();
        },
        fail: (err) => {
          console.error('请求金价API失败', err);
          // 使用默认数据
          this.setDefaultGoldData();
          wx.hideLoading();
          resolve();
        }
      });
    });
  },

  // 设置默认金价数据（当API请求失败时使用）
  setDefaultGoldData: function() {
    this.setData({
      goldPrice: {
        buyPrice: '552.30',
        sellPrice: '552.50',
        updateTime: '2024-05-28 17:22:01',
        internationalPrice: '',
        shanghaiPrice: '552.50',
        changePercent: '+0.16%',
        isUp: true
      },
      marketData: [
        {
          id: 'Au9999',
          name: '黄金9999',
          buyPrice: '552.30',
          sellPrice: '552.50',
          maxPrice: '556.30',
          minPrice: '552.50'
        },
        {
          id: 'PT9995',
          name: '铂金9995',
          buyPrice: '200.50',
          sellPrice: '201.00',
          maxPrice: '205.30',
          minPrice: '199.80'
        }
      ]
    });
  },

  // 跳转到个人中心
  goToProfile: function() {
    wx.switchTab({
      url: '/pages/my/my'
    });
  }
})
